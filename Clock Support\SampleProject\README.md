# Dinesh Hello World VSCode Extension

A simple VSCode extension that displays a beautiful Hello World page with "I am Din<PERSON>" content. Built with TypeScript and React components.

## Features

- 🎉 Beautiful Hello World page with modern design
- 🚀 Built with TypeScript for type safety
- ⚛️ React components for future extensibility
- 🎨 Responsive design with gradient background
- 🔧 Interactive elements with click animations

## Installation

1. Clone or download this project
2. Open the project in VSCode
3. Run `npm install` to install dependencies
4. Press `F5` to run the extension in a new Extension Development Host window
5. In the new window, open the Command Palette (`Ctrl+Shift+P` or `Cmd+Shift+P`)
6. Type "Show Hello World - Dinesh" and select the command

## Development

### Prerequisites

- Node.js (version 16 or higher)
- VSCode

### Setup

```bash
# Install dependencies
npm install

# Compile the extension
npm run compile

# Watch for changes during development
npm run watch
```

### Building

```bash
# Build for production
npm run package
```

## Commands

- `dineshHelloWorld.showHelloWorld`: Shows the Hello World page

## Extension Structure

```
├── src/
│   ├── extension.ts          # Main extension entry point
│   ├── HelloWorldPanel.ts    # Webview panel management
│   ├── components/
│   │   └── HelloWorldComponent.tsx  # React component
│   └── utilities/
│       └── getNonce.ts       # Security utility
├── package.json              # Extension manifest
├── tsconfig.json            # TypeScript configuration
├── webpack.config.js        # Webpack bundling configuration
└── README.md               # This file
```

## Technologies Used

- **TypeScript**: For type-safe development
- **React**: For component-based UI (ready for future enhancements)
- **VSCode Extension API**: For integration with VSCode
- **Webpack**: For bundling and optimization

## License

MIT License - feel free to use and modify as needed!

## Author

Created by Dinesh 👨‍💻
