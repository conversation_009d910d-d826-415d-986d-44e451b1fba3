import * as vscode from 'vscode';

export class MicrochipZephyrSupportProvider implements vscode.TreeDataProvider<vscode.TreeItem> {
  getTreeItem(element: vscode.TreeItem): vscode.TreeItem {
    return element;
  }

  getChildren(element?: vscode.TreeItem): Thenable<vscode.TreeItem[]> {
    if (element) {
      return Promise.resolve([]);
    } else {
      const openSupportPanel = new vscode.TreeItem('Open Support Panel');
      openSupportPanel.command = {
        command: 'microchipZephyr.showHelloWorld',
        title: 'Open Support Panel',
        arguments: [],
      };
      return Promise.resolve([openSupportPanel]);
    }
  }
}